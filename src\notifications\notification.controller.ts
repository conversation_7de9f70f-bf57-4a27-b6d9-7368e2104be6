import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { GetUser } from '../get-user.decorator';
import { GetTenantId } from '../get-tenantId.decorator';
import { ChatUser } from '../models/chat-user.model';
import { WSAuthGuard } from '../user/ws.auth.guard';

@Controller('notifications')
@UseGuards(WSAuthGuard)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  async getUserNotifications(
    @GetUser() user: ChatUser,
    @GetTenantId() tenantId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20
  ) {
    return await this.notificationService.getUserNotifications(
      user.id,
      tenantId,
      page,
      limit
    );
  }

  @Get('pending')
  async getPendingNotifications(
    @GetUser() user: ChatUser,
    @GetTenantId() tenantId: string
  ) {
    return await this.notificationService.getPendingNotifications(user.id, tenantId);
  }

  @Get('unread-count')
  async getUnreadCount(
    @GetUser() user: ChatUser,
    @GetTenantId() tenantId: string
  ) {
    const count = await this.notificationService.getUnreadCount(user.id, tenantId);
    return { count };
  }

  @Post('mark-delivered')
  async markAsDelivered(
    @Body() body: { notificationIds: string[] }
  ) {
    await this.notificationService.markAsDelivered(body.notificationIds);
    return { success: true };
  }

  @Post('mark-read')
  async markAsRead(
    @Body() body: { notificationIds: string[] }
  ) {
    await this.notificationService.markAsRead(body.notificationIds);
    return { success: true };
  }
}
