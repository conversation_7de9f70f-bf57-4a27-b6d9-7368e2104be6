# Robust Notification System for Offline/Online Users

## Overview

This implementation provides a comprehensive notification system that ensures mentioned users receive `NEW_ACTIVITY` events whether they are online or offline. The system combines multiple delivery mechanisms for maximum reliability.

## Key Features

### 1. **Immediate Delivery for Online Users**
- Uses WebSocket connections for real-time delivery
- Employs both direct socket ID targeting and room-based delivery
- Provides immediate feedback when users are actively connected

### 2. **Persistent Storage for Offline Users**
- Stores notifications in MongoDB for later retrieval
- Maintains notification status (PENDING, DELIVERED, READ)
- Supports notification history and audit trails

### 3. **Push Notifications for Offline Users**
- Integrates with RabbitMQ for external push notification services
- Sends push notifications when users are offline
- Includes rich notification data for client processing

### 4. **Automatic Delivery on Reconnection**
- Delivers pending notifications when users come back online
- Marks notifications as delivered automatically
- Provides notification count and summary

## Implementation Components

### Core Models

#### `Notification` Model
```typescript
export class Notification {
  recipient: string;      // User who should receive the notification
  sender: string;         // User who triggered the notification
  chat: string;          // Chat where the event occurred
  message?: string;      // Related message (for mentions)
  activity?: string;     // Related activity record
  type: NotificationType; // MENTION, REACTION, INVITE, etc.
  status: NotificationStatus; // PENDING, DELIVERED, READ
  title: string;         // Notification title
  body: string;          // Notification content
  data?: any;           // Additional metadata
  tenantId: string;     // Workspace/tenant isolation
}
```

#### `NotificationType` Enum
- `MENTION`: User mentioned in a message
- `REACTION`: Reaction added to user's message  
- `INVITE`: User invited to chat
- `MESSAGE`: New message notification
- `CALL`: Call-related notifications

#### `NotificationStatus` Enum
- `PENDING`: Not yet delivered to user
- `DELIVERED`: Sent to user but not read
- `READ`: Acknowledged by user

### Service Architecture

#### `NotificationService`
- **Purpose**: Core CRUD operations for notifications
- **Key Methods**:
  - `createNotification()`: Store new notification
  - `getPendingNotifications()`: Get undelivered notifications
  - `markAsDelivered()`: Update delivery status
  - `markAsRead()`: Update read status
  - `getUserNotifications()`: Paginated notification history

#### `NotificationDeliveryService`
- **Purpose**: Orchestrates notification delivery across all channels
- **Key Features**:
  - Smart routing (online vs offline users)
  - Multi-channel delivery (WebSocket, Push, Email)
  - Automatic retry and fallback mechanisms
  - Delivery confirmation and tracking

### Delivery Flow

```mermaid
graph TD
    A[User Mentions Another User] --> B[Create Activity]
    B --> C[Check if Mentioned User is Online]
    C --> D{User Online?}
    
    D -->|Yes| E[Direct WebSocket Delivery]
    D -->|No| F[Store in Database]
    
    E --> G[Mark as Delivered]
    F --> H[Send Push Notification]
    
    H --> I[User Comes Online]
    I --> J[Deliver Pending Notifications]
    J --> K[Mark as Delivered]
    
    G --> L[User Reads Notification]
    K --> L
    L --> M[Mark as Read]
```

### Integration Points

#### WebSocket Events
New events added to `CHAT_EVENTS`:
- `GET_NOTIFICATIONS`: Retrieve user notifications
- `GET_NOTIFICATION_COUNT`: Get unread notification count
- `MARK_NOTIFICATIONS_READ`: Mark notifications as read

#### Push Notification Integration
- Uses existing RabbitMQ infrastructure
- Sends to `notification-message-queue`
- Compatible with external push services

#### Database Schema
- New `notifications` collection in MongoDB
- Indexed by `recipient`, `tenantId`, `status`
- Includes timestamps for audit trails

## Usage Examples

### For Mentions (Implemented)
```typescript
// When a user is mentioned in a message
const notificationData = {
  recipient: mentionedUser.id,
  sender: currentUser.id,
  chat: chatId,
  message: messageId,
  activity: activityId,
  type: NotificationTypeEnum.MENTION,
  title: `New mention in ${chatName}`,
  body: `You are mentioned in a chat message in ${chatName}`,
  tenantId: tenantId
};

await this.notificationDeliveryService.deliverNotification(
  this.server,
  mentionedUser.id,
  this.users,
  notificationData,
  activityPayload,
  {
    immediate: true,  // WebSocket if online
    persist: true,    // Store in database
    push: true,       // Push notification if offline
    email: true       // Email notification
  }
);
```

### For Reactions (Implemented)
```typescript
// When someone reacts to a user's message
const notificationData = {
  recipient: messageAuthor.id,
  sender: currentUser.id,
  chat: chatId,
  message: messageId,
  activity: activityId,
  type: NotificationTypeEnum.REACTION,
  title: `New reaction in ${chatName}`,
  body: `You have a new reaction in a chat message`,
  tenantId: tenantId
};
```

### Client-Side Usage
```typescript
// Get notification count
socket.emit('chat-event', {
  event: 'GET_NOTIFICATION_COUNT',
  data: {}
});

// Get notifications with pagination
socket.emit('chat-event', {
  event: 'GET_NOTIFICATIONS',
  data: { page: 1, limit: 20 }
});

// Mark notifications as read
socket.emit('chat-event', {
  event: 'MARK_NOTIFICATIONS_READ',
  data: { notificationIds: ['id1', 'id2'] }
});
```

## Configuration Options

### Delivery Options
```typescript
interface NotificationDeliveryOptions {
  immediate?: boolean; // Send immediately to online users
  persist?: boolean;   // Store in database for offline users
  push?: boolean;      // Send push notification to offline users
  email?: boolean;     // Send email notification
}
```

### Default Settings
- **Mentions**: immediate + persist + push + email
- **Reactions**: immediate + persist + push (no email)
- **Invites**: immediate + persist + push + email
- **Calls**: immediate + push (no persist for urgency)

## Benefits

### 1. **Reliability**
- Multiple delivery channels ensure notifications reach users
- Database persistence prevents notification loss
- Automatic retry mechanisms handle temporary failures

### 2. **User Experience**
- Real-time delivery for online users
- Offline users don't miss important notifications
- Automatic delivery on reconnection

### 3. **Scalability**
- Asynchronous processing via RabbitMQ
- Database indexing for efficient queries
- Configurable delivery options per notification type

### 4. **Observability**
- Detailed logging and tracking
- Notification status monitoring
- Delivery confirmation mechanisms

### 5. **Flexibility**
- Pluggable delivery channels
- Configurable notification types
- Extensible for future requirements

## Future Enhancements

1. **Notification Preferences**: Allow users to configure notification settings
2. **Rate Limiting**: Prevent notification spam
3. **Rich Notifications**: Support for images, actions, etc.
4. **Analytics**: Track notification engagement metrics
5. **Templates**: Configurable notification templates
6. **Batch Processing**: Optimize for high-volume scenarios

## Monitoring and Maintenance

### Key Metrics to Monitor
- Notification delivery success rates
- Average delivery time
- Database storage growth
- Push notification response rates

### Maintenance Tasks
- Regular cleanup of old read notifications
- Monitor notification queue health
- Update push notification tokens
- Review delivery failure patterns

This implementation ensures that mentioned users always receive their notifications, regardless of their online status, providing a robust foundation for real-time communication features.
