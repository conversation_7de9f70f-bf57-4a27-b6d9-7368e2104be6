import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification, NotificationStatus, NotificationType } from './notification.model';
import { LogService } from '../log/log.service';

export interface CreateNotificationDto {
  recipient: string;
  sender: string;
  chat: string;
  message?: string;
  activity?: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: any;
  tenantId: string;
}

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(Notification.name) private notificationModel: Model<Notification>,
    private logService: LogService,
  ) {}

  async createNotification(dto: CreateNotificationDto): Promise<Notification> {
    try {
      const notification = new this.notificationModel(dto);
      const saved = await notification.save();
      this.logService.log('Notification created:', saved.id);
      return saved;
    } catch (error) {
      this.logService.error('Error creating notification:', error);
      throw error;
    }
  }

  async getPendingNotifications(userId: string, tenantId: string): Promise<Notification[]> {
    try {
      return await this.notificationModel
        .find({
          recipient: userId,
          tenantId,
          status: NotificationStatus.PENDING
        })
        .populate('sender', 'username email jobProUserId')
        .populate('chat', 'name chatType')
        .populate('message', 'text messageType')
        .populate('activity')
        .sort({ createdAt: -1 })
        .exec();
    } catch (error) {
      this.logService.error('Error getting pending notifications:', error);
      return [];
    }
  }

  async markAsDelivered(notificationIds: string[]): Promise<void> {
    try {
      await this.notificationModel.updateMany(
        { _id: { $in: notificationIds } },
        { 
          status: NotificationStatus.DELIVERED,
          deliveredAt: new Date()
        }
      );
      this.logService.log(`Marked ${notificationIds.length} notifications as delivered`);
    } catch (error) {
      this.logService.error('Error marking notifications as delivered:', error);
    }
  }

  async markAsRead(notificationIds: string[]): Promise<void> {
    try {
      await this.notificationModel.updateMany(
        { _id: { $in: notificationIds } },
        { 
          status: NotificationStatus.READ,
          readAt: new Date()
        }
      );
      this.logService.log(`Marked ${notificationIds.length} notifications as read`);
    } catch (error) {
      this.logService.error('Error marking notifications as read:', error);
    }
  }

  async getUnreadCount(userId: string, tenantId: string): Promise<number> {
    try {
      return await this.notificationModel.countDocuments({
        recipient: userId,
        tenantId,
        status: { $in: [NotificationStatus.PENDING, NotificationStatus.DELIVERED] }
      });
    } catch (error) {
      this.logService.error('Error getting unread count:', error);
      return 0;
    }
  }

  async deleteOldNotifications(daysOld: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const result = await this.notificationModel.deleteMany({
        createdAt: { $lt: cutoffDate },
        status: NotificationStatus.READ
      });
      
      this.logService.log(`Deleted ${result.deletedCount} old notifications`);
    } catch (error) {
      this.logService.error('Error deleting old notifications:', error);
    }
  }

  async getUserNotifications(
    userId: string, 
    tenantId: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<{ notifications: Notification[], total: number, hasMore: boolean }> {
    try {
      const skip = (page - 1) * limit;
      
      const [notifications, total] = await Promise.all([
        this.notificationModel
          .find({ recipient: userId, tenantId })
          .populate('sender', 'username email jobProUserId')
          .populate('chat', 'name chatType')
          .populate('message', 'text messageType')
          .populate('activity')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.notificationModel.countDocuments({ recipient: userId, tenantId })
      ]);

      return {
        notifications,
        total,
        hasMore: skip + notifications.length < total
      };
    } catch (error) {
      this.logService.error('Error getting user notifications:', error);
      return { notifications: [], total: 0, hasMore: false };
    }
  }
}
